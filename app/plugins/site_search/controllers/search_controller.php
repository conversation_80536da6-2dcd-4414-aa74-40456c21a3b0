<?php

class SearchController extends SiteSearchAppController {

  var $paginate = array(
    'limit' => 10,
    'page' => 1
  );

  public $criticalCss = 'search';

  function beforeFilter() {
    parent::beforeFilter();

    // Ensure Auth component is properly initialized and allowedActions is an array
    if (isset($this->Auth) && is_object($this->Auth)) {
      if (!is_array($this->Auth->allowedActions)) {
        $this->Auth->allowedActions = array();
      }
      $this->Auth->allowedActions = array_merge($this->Auth->allowedActions, array('test','results'));
    }
  }


  function results() {
    $section = '';
    $sectionTitle = '';
    $heroBannerImage = RESOURCES_HOSTNAME . '/img/site/layout/search/hero-banner.jpg';
    $navigation = array();
    $term = '';

    $breadcrumbs = array(array(
      'text' => 'Search Results',
      'url'  => $this->here
    ));

    // Get search term from GET parameter
    if (!empty($_GET['search'])) {
      $term = $_GET['search'];
    } elseif (isset($this->params['term'])) {
      $term = str_replace('%20', ' ', $this->params['term']);
    }

    if (isset($this->passedArgs['show'])) {
      $this->paginate['limit'] = $this->passedArgs['show'];
    }

    if (isset($this->passedArgs['page'])) {
      $this->paginate['page'] = $this->passedArgs['page'];
    }

    $this->Search->paginate = $this->paginate;

    $results = $this->paginate('Search', array('term' => $term));

    $this->set(compact('results', 'term', 'navigation', 'section', 'sectionTitle', 'heroBannerImage', 'breadcrumbs'));
  }
}
