/**
 * Search toggle functionality for the blog
 * This script handles the search toggle button click event
 * and toggles the search form visibility on mobile only
 *
 * This script runs immediately to fix the search toggle before the main site's JS runs
 */
(function() {
    // Set a flag to indicate this script has run
    window.searchToggleFixed = true;

    console.log('[Search Toggle] Initializing search toggle functionality');

    // Function to initialize the search toggle
    function initSearchToggle() {
        // Find the search toggle button
        const searchToggle = document.querySelector('.search-toggle');

        if (searchToggle) {
            console.log('[Search Toggle] Search toggle button found');

            // Remove any existing click event listeners by cloning the element
            const newSearchToggle = searchToggle.cloneNode(true);
            searchToggle.parentNode.replaceChild(newSearchToggle, searchToggle);

            // Add our click event listener - only for mobile
            newSearchToggle.addEventListener('click', function(e) {
                // Only handle the toggle on mobile screens
                if (window.innerWidth < 1025) {
                    e.preventDefault();
                    e.stopPropagation(); // Prevent event bubbling

                    // Toggle 'active' class on the button
                    this.classList.toggle('active');

                    // Always use the search element ID regardless of data-toggle attribute
                    const targetElement = document.getElementById('search');

                    if (targetElement) {
                        // Toggle 'active' class on the target element
                        targetElement.classList.toggle('active');

                        // Log the current state for debugging
                        const isActive = targetElement.classList.contains('active');
                        console.log('[Search Toggle] Toggled search form visibility. Active:', isActive);
                        console.log('[Search Toggle] Search form display style:', window.getComputedStyle(targetElement).display);

                        // Force display style if needed
                        if (isActive && window.getComputedStyle(targetElement).display === 'none') {
                            console.log('[Search Toggle] Forcing display style to block');
                            targetElement.style.display = 'block';
                        }
                    } else {
                        console.error('[Search Toggle] Search element not found');
                        // Try to find by class as a fallback
                        const searchByClass = document.querySelector('.primary-search');
                        if (searchByClass) {
                            console.log('[Search Toggle] Found search form by class');
                            searchByClass.classList.toggle('active');
                            if (searchByClass.classList.contains('active')) {
                                searchByClass.style.display = 'block';
                            }
                        }
                    }
                } else {
                    console.log('[Search Toggle] Ignoring click on desktop - search form is always visible');
                }
            });

            // Prevent the original App.Navigation from handling this element
            // by removing the data-toggle attribute
            if (newSearchToggle.hasAttribute('data-toggle')) {
                console.log('[Search Toggle] Removing data-toggle attribute');
                newSearchToggle.removeAttribute('data-toggle');
            } else {
                console.log('[Search Toggle] No data-toggle attribute found, already removed');
            }

            // Add our own attribute to mark this as fixed
            newSearchToggle.setAttribute('data-search-toggle-fixed', 'true');
        } else {
            console.error('[Search Toggle] Search toggle button not found');
        }
    }

    // Try to run as early as possible
    initSearchToggle();

    // Also run on DOMContentLoaded as a fallback
    document.addEventListener('DOMContentLoaded', function() {
        // Only run again if the button doesn't have our marker attribute
        if (!document.querySelector('.search-toggle[data-search-toggle-fixed]')) {
            console.log('[Search Toggle] Reinitializing on DOMContentLoaded');
            initSearchToggle();
        }
    });

    // Also run when the window loads, as a final fallback
    window.addEventListener('load', function() {
        // Only run again if the button doesn't have our marker attribute
        if (!document.querySelector('.search-toggle[data-search-toggle-fixed]')) {
            console.log('[Search Toggle] Reinitializing on window load');
            initSearchToggle();
        }
    });
})();
