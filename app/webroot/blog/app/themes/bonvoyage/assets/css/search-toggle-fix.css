/**
 * CSS fixes for the search toggle button and search form
 * Based on the main site's styling
 */

/* Search toggle button styling */
.search-toggle {
    position: absolute;
    top: 23px;
    right: 19px;
    cursor: pointer;
    background: transparent;
    border: none;
    padding: 0;
    width: 25px;
    height: 25px;
    background: url('/img/site/icons/search.svg') no-repeat;
    background-position: 0 0 !important;
    z-index: 10;
}

/* Mobile search toggle */
@media (max-width: 767px) {
    .search-toggle {
        top: 23px;
        right: 19px;
        display: block;
    }
    .primary-search--page-header {
        padding: 0!important;
        max-width: 100%;
    }
}

/* Hide search toggle on desktop */
@media (min-width: 1024px) {
    .search-toggle {
        display: none; /* Hide the toggle on desktop */
        margin: 0 20px;
    }
      .primary-search__input .button-wrap button {
      background: url('/img/site/icons/search_grey.svg') no-repeat;
  }
}

.search-toggle:hover {
    /* opacity: 0.8; */
}

/* Search form styling */
.primary-search {
    margin-top: 18px;
    padding: 10px 0;
    border-top: 1px dashed rgba(255, 255, 255, 0.3);
    border-bottom: 1px dashed rgba(255, 255, 255, 0.3);
}

/* Hide search form by default on mobile */
@media (max-width: 1023px) {
    .primary-search {
        display: none;
    }

    /* Show search form when active on mobile */
    .primary-search.active {
        display: block !important;
    }

    /* Additional selector to ensure the search form is visible on mobile */
    #search.active {
        display: block !important;
    }
}

/* Always show search form on desktop */
@media (min-width: 768px) {
    .primary-search {
        display: block !important;
        padding: 0;
        margin-top: 0;
        border-top: none;
        border-bottom: none;
    }
}

.primary-search input[type=text] {
    border-radius: 2px;
    border: 0;
    height: 35px;
    width: 100%;
    padding: 12px;
    color: #565044;
}

.primary-search input[type=submit] {
    position: absolute;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
}

.primary-search button {
    background: transparent;
    border: none;
    cursor: pointer;
    margin: 0;
}

/* Ensure the search form is positioned correctly */
.primary-search--page-header {
    position: absolute;
    top: 100%;
    right: 0;
    width: 100%;
    max-width: 300px;
    background-color: #fff;
    z-index: 100;
    /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); */
    border-radius: 5px;
}

/* Desktop search positioning */
@media (min-width: 768px) {
    .primary-search--page-header {
        position: absolute;
        top: 22px;
        right: 16px;
        width: 200px;
        margin-top: 0;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .primary-search--page-header {
        width: 150px;
    }
}

.primary-search__form {
    overflow: hidden;
}

.primary-search__input {
    width: auto;
    overflow: hidden;
    position: relative;
}

/* Button wrap styling */
.button-wrap {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    width: 40px;
    align-items: center;
    justify-content: center;
}

/* Ensure the search toggle image is visible */
.search-toggle img {
    display: block;
}
