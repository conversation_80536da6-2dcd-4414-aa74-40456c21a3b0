<?php

class DestinationsController extends AppController {

  var $name = 'Destinations';
  var $components = array('Auth', 'RequestHandler', 'Navigation', 'Section');

  function beforeFilter() {
    parent::beforeFilter();
    // Allow healthcheck action without authentication - only for this controller
    $this->Auth->allowedActions = array_merge(
      (array)$this->Auth->allowedActions,
      array('healthcheck')
    );
  }

  public function beforeRender() {
    parent::beforeRender();

    // Get ALL USA regions (including A-Z listings)
    $usaDestinations = $this->Destination->find('all', array(
        'conditions' => array(
            'Destination.parent_id' => 1,  // USA Holidays
            'Destination.published' => 1
        ),
        'recursive' => -1,
        'order' => array('Destination.lft ASC')
    ));

    // Get ALL Canada regions
    $canadaDestinations = $this->Destination->find('all', array(
        'conditions' => array(
            'Destination.parent_id' => 2,
            'Destination.published' => 1
        ),
        'recursive' => -1,
        'order' => array('Destination.lft ASC')
    ));

    // Get holiday types - note these are from holiday_types table
    $holidayTypes = ClassRegistry::init('HolidayType')->find('all', array(
        'conditions' => array('HolidayType.published' => 1),
        'recursive' => -1,
        'order' => array('HolidayType.name ASC')
    ));

    $this->set(compact('usaDestinations', 'canadaDestinations', 'holidayTypes'));
  }

  function webadmin_edit($id = null) {
    error_log("[Navigation] Starting destination edit for ID: " . $id);

    parent::webadmin_edit($id);

    if ($this->data) {
        error_log("[Navigation] Clearing cache due to destination update");
        Cache::delete('main_navigation', 'navigation');
    }

    $mapData = $this->Destination->getMapData($id, 'editor');
    $this->set(compact('mapData'));
  }

  function view() {

    //This controller uses the section component. The component already
    //returns the destination data and calls a 404 if not found. So in
    //theory it should never reach here if the data is empty, but just
    //in case, here's another check
    if (empty($this->sectionData['Destination'])) {
      $this->cakeError('error404');
    }

    $destination = $this->sectionData;

    $destinationActivities = $this->_findActivities($this->sectionId);

    $related = $this->_findHolidayTypes($this->sectionId);

    $this->set(compact('destination', 'destinationActivities', 'related', 'breadcrumbs'));
  }

  function healthcheck() {
    $this->view();
  }

  /**
   * Returns the children of a destination
   */
  protected function _findChildren($sectionId) {
    $findChildren = function() use ($sectionId) {
      return @$this->Destination->children(
        $sectionId, true, null, null, null, null, 0
      );
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId,  'children'
    )), $findChildren);
  }

  /**
   * Returns a destinations related activities
   */
  protected function _findActivities($sectionId) {
    $findActivities = function() use ($sectionId) {
      return ClassRegistry::init('Activity')->getActivitiesByDestination($sectionId);
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId, 'activities'
    )), $findActivities);
  }

  /**
   * Returns a destinations related holiday types
   */
  protected function _findHolidayTypes($sectionId) {
    $findHolidayTypes = function() use ($sectionId) {
      return ClassRegistry::init('HolidayType')->getHolidayTypesOnDestination($sectionId);
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId, 'holiday_types'
    )), $findHolidayTypes);
  }
}

?>
