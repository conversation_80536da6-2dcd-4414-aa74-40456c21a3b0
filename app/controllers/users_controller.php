<?php

class UsersController extends AppController {

  var $name = 'Users';
  var $components = array('Auth', 'Cookie', 'Session');

  function beforeFilter() {
    // For login action, we need some Auth setup but not the full parent beforeFilter
    if ($this->action === 'webadmin_login') {
      // Set webadmin layout manually
      $this->layout = 'webadmin';

      // Initialize Auth component manually with minimal config
      if (isset($this->Auth)) {
        $this->Auth->allowedActions = array('webadmin_login');
        $this->Auth->fields = array('username' => 'email', 'password' => 'password');
        $this->Auth->userModel = 'User';
        $this->Auth->autoRedirect = false;
      }
    } else {
      parent::beforeFilter();
    }

    if (in_array($this->action, array('webadmin_add', 'webadmin_edit', 'webadmin_my_account'))) {
      if (isset($this->Auth)) {
        $this->Auth->authenticate = $this->User;
      }
    }
    if (in_array($this->action, array('webadmin_edit'))) {
      unset($this->User->validate['old_password']);
    }
  }

  function webadmin_my_account() {

    if (!$is = $this->Auth->user('id')) {
      $this->Session->setFlash(__('Unknown user ID', true), 'webadmin_flash_bad');
      $this->History->back(0);
    }

    parent::webadmin_edit($this->Auth->user('id'));

    if (empty($this->params['form'])) {
      unset($this->data['User']['password']);
    }

  }

  function webadmin_login() {
    // Show available users for testing (remove this in production)
    if (empty($this->data)) {
      $users = $this->User->find('all', array(
        'fields' => array('User.id', 'User.email'),
        'recursive' => -1,
        'limit' => 5
      ));
      $this->set('debug_users', $users);
    }

    // Process login form submission
    if (!empty($this->data)) {
      // Use Auth component's login method
      if ($this->Auth->login()) {
        // Login successful

        // Set user profile and permissions
        if ($webadminUserProfile = $this->User->WebadminUserProfile->find('first', array('conditions' => array('user_id' => $this->Auth->user('id')), 'recursive' => -1))) {
          $this->Session->write('Auth.WebadminUserProfile', $webadminUserProfile['WebadminUserProfile']);
        }
        $this->Session->write('Auth.Permissions', $this->User->permissions($this->Auth->user('id')));

        // Redirect to admin dashboard
        $this->redirect(array('webadmin' => true, 'controller' => 'destinations', 'action' => 'index'));
      } else {
        // Login failed - Auth component handles the error message
        $this->Session->setFlash('Invalid email or password', 'webadmin_flash_bad');
      }
    }
  }

  function webadmin_logout() {
    $this->Session->setFlash('You\'re logged out', 'webadmin_flash_good');
    $this->Cookie->del('Auth');
    $this->redirect($this->Auth->logout());
  }

  function webadmin_home() {

  }

  function webadmin_add($parentId = null) {

    extract($this->{$this->modelClass}->getInfo());

    if (!empty($this->data)) {
      $this->$modelClass->create();
      if ($this->$modelClass->saveAll($this->data)) {
        $this->Session->setFlash(__("The $singularHumanName has been saved", true), 'webadmin_flash_good');
        if (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Add Another', true)) {
          $this->History->back(0);
        } elseif (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Go Back', true)) {
          $this->History->back();
        } else {
          $this->redirect(array('action' => 'edit', $this->$modelClass->getInsertID()));
        }

      } else {
        $this->Session->setFlash(__("The $singularHumanName could not be saved. Please, try again.", true), 'webadmin_flash_bad');
      }
    } else {
      $this->_setDataFromParams();
    }

    $this->_setAssociatedLists();
    $this->_setEnumLists();
    $this->_setModelInfo();
    $this->_setHiddenFields();

    $this->pageTitle = __('Add ', true) . $singularHumanName;

  }

}

?>
