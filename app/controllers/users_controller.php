<?php

class UsersController extends AppController {

  var $name = 'Users';
  var $components = array('Auth', 'Cookie');

  function beforeFilter() {
    parent::beforeFilter();

    // Allow login action without authentication
    $this->Auth->allowedActions = array('webadmin_login');

    if (in_array($this->action, array('webadmin_add', 'webadmin_edit', 'webadmin_my_account'))) {
      $this->Auth->authenticate = $this->User;
    }
    if (in_array($this->action, array('webadmin_edit'))) {
      unset($this->User->validate['old_password']);
    }
  }

  function webadmin_my_account() {

    if (!$is = $this->Auth->user('id')) {
      $this->Session->setFlash(__('Unknown user ID', true), 'webadmin_flash_bad');
      $this->History->back(0);
    }

    parent::webadmin_edit($this->Auth->user('id'));

    if (empty($this->params['form'])) {
      unset($this->data['User']['password']);
    }

  }

  function webadmin_login() {
    // If user is already logged in, redirect them
    if ($this->Auth->user()) {
      $this->redirect($this->Auth->redirect());
    }

    // Try to login with cookie if no form data and cookie exists
    if (empty($this->data)) {
      $cookie = $this->Cookie->read('Auth.'.$this->Auth->userModel);
      if ($cookie && $this->Auth->login($cookie)) {
        $this->Session->del('Message.auth');
        $this->redirect($this->Auth->redirect());
      }
    }

    // Process login form submission
    if (!empty($this->data)) {
      if ($this->Auth->login()) {
        // Login successful
        if (!empty($this->data['User']['remember_me'])) {
          $cookie = array(
            $this->Auth->fields['username'] => $this->data['User'][$this->Auth->fields['username']],
            $this->Auth->fields['password'] => $this->data['User'][$this->Auth->fields['password']]
          );
          $this->Cookie->write('Auth.'.$this->Auth->userModel, $cookie, true, '+2 weeks');
        }

        // Set user profile and permissions
        if ($webadminUserProfile = $this->User->WebadminUserProfile->find('first', array('conditions' => array('user_id' => $this->Auth->user('id')), 'recursive' => -1))) {
          $this->Session->write('Auth.WebadminUserProfile', $webadminUserProfile['WebadminUserProfile']);
        }
        $this->Session->write('Auth.Permissions', $this->User->permissions($this->Auth->user('id')));

        $this->redirect($this->Auth->redirect());
      } else {
        // Login failed
        $this->Session->setFlash('Invalid email or password', 'webadmin_flash_bad');
      }
    }
  }

  function webadmin_logout() {
    $this->Session->setFlash('You\'re logged out', 'webadmin_flash_good');
    $this->Cookie->del('Auth');
    $this->redirect($this->Auth->logout());
  }

  function webadmin_home() {

  }

  function webadmin_add($parentId = null) {

    extract($this->{$this->modelClass}->getInfo());

    if (!empty($this->data)) {
      $this->$modelClass->create();
      if ($this->$modelClass->saveAll($this->data)) {
        $this->Session->setFlash(__("The $singularHumanName has been saved", true), 'webadmin_flash_good');
        if (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Add Another', true)) {
          $this->History->back(0);
        } elseif (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Go Back', true)) {
          $this->History->back();
        } else {
          $this->redirect(array('action' => 'edit', $this->$modelClass->getInsertID()));
        }

      } else {
        $this->Session->setFlash(__("The $singularHumanName could not be saved. Please, try again.", true), 'webadmin_flash_bad');
      }
    } else {
      $this->_setDataFromParams();
    }

    $this->_setAssociatedLists();
    $this->_setEnumLists();
    $this->_setModelInfo();
    $this->_setHiddenFields();

    $this->pageTitle = __('Add ', true) . $singularHumanName;

  }

}

?>
