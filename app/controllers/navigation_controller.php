<?php
// file_put_contents(dirname(__FILE__) . '/../tmp/logs/navigation_controller_loaded.txt', 'loaded:' . date('c') . "\n", FILE_APPEND);

/**
 * Navigation Controller
 *
 * Provides API endpoints for navigation elements that can be consumed by the WordPress blog
 */
class NavigationController extends AppController {

    var $name = 'Navigation';
    var $uses = array('Destination', 'HolidayType', 'Spotlight', 'Page');
    var $components = array('Navigation', 'RequestHandler');
    var $helpers = array('App');

    /**
     * Before filter - allow API endpoints without authentication
     */
    function beforeFilter() {
        parent::beforeFilter();

        // Allow API endpoints without authentication - only for this controller
        $this->Auth->allowedActions = array('megamenu', 'mmenu');

        // Set JSON response type for API endpoints
        if (in_array($this->action, array('megamenu', 'mmenu'))) {
            $this->RequestHandler->respondAs('html');
            $this->layout = 'ajax';
        }
    }

    /**
     * API endpoint to get the megamenu HTML
     * This can be called from the WordPress blog to get the latest megamenu content
     */
    function megamenu() {
        // Get navigation data
        $navigationData = $this->Navigation->getNavigationData();

        // Extract navigation components
        $mainNav = $navigationData['mainNav'];
        $usaDestinations = $navigationData['usaDestinations'];
        $canadaDestinations = $navigationData['canadaDestinations'];
        $holidayTypes = $navigationData['holidayTypes'];
        $whatsHot = $navigationData['whatsHot'];
        $holidayInfoPages = $navigationData['holidayInfoPages'];
        $aboutPages = $navigationData['aboutPages'];

        // Set variables for the view
        $this->set(compact('mainNav', 'usaDestinations', 'canadaDestinations', 'holidayTypes', 'whatsHot', 'holidayInfoPages', 'aboutPages'));

        // Render the megamenu element
        $this->render('/elements/chrome/mega_menu_api');
    }

    /**
     * API endpoint to get the mmenu HTML
     * This can be called from the WordPress blog to get the latest mobile menu content
     */
    function mmenu() {
        // Get navigation data
        $navigationData = $this->Navigation->getNavigationData();

        // Extract navigation components
        $mainNav = $navigationData['mainNav'];
        $usaDestinations = $navigationData['usaDestinations'];
        $canadaDestinations = $navigationData['canadaDestinations'];
        $holidayTypes = $navigationData['holidayTypes'];
        $whatsHot = $navigationData['whatsHot'];
        $holidayInfoPages = $navigationData['holidayInfoPages'];
        $aboutPages = $navigationData['aboutPages'];

        // Build mobile navigation structure
        $mobileNavigation = array();

        // Add USA section
        if (!empty($mainNav['usa']['items'])) {
            $mobileNavigation[] = array(
                'text' => 'USA',
                'url' => '/destinations/usa_holidays',
                'has_children' => true,
                'children' => array_map(function ($dest) {
                    $item = array(
                        'text' => $dest['Destination']['name'],
                        'url' => '/destinations/' . $dest['Destination']['slug']
                    );

                    // Add child destinations if they exist
                    if (!empty($dest['Destination']['children'])) {
                        $item['has_children'] = true;
                        $item['children'] = array_map(function ($child) {
                            return array(
                                'text' => $child['Destination']['name'],
                                'url' => '/destinations/' . $child['Destination']['slug']
                            );
                        }, $dest['Destination']['children']);
                    }

                    return $item;
                }, $mainNav['usa']['items'])
            );
        }

        // Add Canada section
        if (!empty($mainNav['canada']['items'])) {
            $mobileNavigation[] = array(
                'text' => 'Canada',
                'url' => '/destinations/canada_holidays',
                'has_children' => true,
                'children' => array_map(function ($dest) {
                    $item = array(
                        'text' => $dest['Destination']['name'],
                        'url' => '/destinations/' . $dest['Destination']['slug']
                    );

                    // Add child destinations if they exist
                    if (!empty($dest['Destination']['children'])) {
                        $item['has_children'] = true;
                        $item['children'] = array_map(function ($child) {
                            return array(
                                'text' => $child['Destination']['name'],
                                'url' => '/destinations/' . $child['Destination']['slug']
                            );
                        }, $dest['Destination']['children']);
                    }

                    return $item;
                }, $mainNav['canada']['items'])
            );
        }

        // Add Holiday Types section
        if (!empty($holidayTypes)) {
            $mobileNavigation[] = array(
                'text' => 'Holiday Types',
                'url' => '/holidays',
                'has_children' => true,
                'children' => array_map(function ($type) {
                    return array(
                        'text' => $type['HolidayType']['name'],
                        'url' => '/holidays/' . $type['HolidayType']['slug']
                    );
                }, $holidayTypes)
            );
        }

        // Add What's Hot section
        if (!empty($whatsHot)) {
            $mobileNavigation[] = array(
                'text' => 'What\'s Hot',
                'url' => '/spotlights',
                'has_children' => true,
                'children' => array_map(function ($spotlight) {
                    return array(
                        'text' => $spotlight['Spotlight']['name'],
                        'url' => '/spotlights/' . $spotlight['Spotlight']['slug']
                    );
                }, $whatsHot)
            );
        }

        // Add Holiday Info section
        if (!empty($holidayInfoPages)) {
            $mobileNavigation[] = array(
                'text' => 'Holiday Info',
                'url' => '/page/holiday_information',
                'has_children' => true,
                'children' => array_map(function ($page) {
                    return array(
                        'text' => $page['Page']['internal_ref'],
                        'url' => '/page/' . $page['Page']['slug']
                    );
                }, $holidayInfoPages)
            );
        }

        // Add About Us section
        if (!empty($aboutPages)) {
            $mobileNavigation[] = array(
                'text' => 'About Us',
                'url' => '/page/about_bon_voyage',
                'has_children' => true,
                'children' => array_map(function ($page) {
                    return array(
                        'text' => $page['Page']['internal_ref'],
                        'url' => '/page/' . $page['Page']['slug']
                    );
                }, $aboutPages)
            );
        }

        // Add primary nav items
        $mobileNavigation[] = array(
            'text' => 'Blog',
            'url' => '/blog'
        );
        $mobileNavigation[] = array(
            'text' => 'FAQs',
            'url' => '/faqs'
        );
        $mobileNavigation[] = array(
            'text' => 'Make an Enquiry',
            'url' => '/make_an_enquiry'
        );

        // Set variables for the view
        $this->set(compact('mobileNavigation'));

        // Render the mmenu element
        $this->render('/elements/chrome/mmenu_api');
    }
}
?>
